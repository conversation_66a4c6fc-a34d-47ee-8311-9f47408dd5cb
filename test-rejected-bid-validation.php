<?php
/**
 * Test script for rejected bid validation functionality
 * 
 * This script tests the new rejected bid validation logic to ensure:
 * 1. Users cannot bid lower than their highest rejected bid
 * 2. Minimum bid requirements are correctly calculated
 * 3. Validation messages are appropriate
 * 
 * To run this test:
 * 1. Place this file in your WordPress root directory
 * 2. Access it via browser: yoursite.com/test-rejected-bid-validation.php
 * 3. Review the test results
 */

// Load WordPress
require_once('wp-config.php');

// Ensure we're in a WordPress environment
if (!defined('ABSPATH')) {
    die('WordPress not loaded');
}

echo "<h1>Rejected Bid Validation Test</h1>";

// Test 1: Debug the specific $84,899.50 issue
echo "<h2>Test 1: Debug Specific Issue</h2>";

// Create a test scenario (you'll need to adjust these values based on your actual data)
$test_lot_id = 'TEST001'; // Replace with an actual lot ID from your database
$test_user_id = 1; // Replace with an actual user ID

echo "<h3>Debugging the $84,899.50 minimum calculation issue:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>";
echo "<p><strong>Expected:</strong> User with $90,000.00 rejected bid should see minimum of $90,000.01</p>";
echo "<p><strong>Actual:</strong> User sees minimum of $84,899.50</p>";
echo "<p><strong>Investigation:</strong></p>";

// Test the function
$highest_rejected = USF_Database::get_user_highest_rejected_bid_amount($test_lot_id, $test_user_id);
echo "1. Highest rejected bid for user $test_user_id on lot $test_lot_id: ";
echo $highest_rejected ? '$' . number_format($highest_rejected, 2) : 'None';
echo "<br>";

// Check if there are any pending bids
$pending_bid = USF_Database::get_user_pending_bid_amount($test_lot_id, $test_user_id);
echo "2. User pending bid: ";
echo $pending_bid ? '$' . number_format($pending_bid, 2) : 'None';
echo "<br>";

// Get the auction details
$auction = USF_Database::get_lot($test_lot_id);
if ($auction) {
    echo "3. Auction starting price (min_offer): $" . number_format($auction->min_offer, 2) . "<br>";

    // Simulate the minimum calculation logic
    $minimum_bid_amount = $auction->min_offer;
    $minimum_bid_reason = 'starting price';

    if ($pending_bid) {
        $minimum_bid_amount = $pending_bid + 0.01;
        $minimum_bid_reason = 'current bid';
    } else if ($highest_rejected) {
        // For rejected bids, minimum must exceed the rejected amount (not revert to starting price)
        $minimum_bid_amount = $highest_rejected + 0.01;
        $minimum_bid_reason = 'rejected bid';
    }

    echo "4. Calculated minimum: $" . number_format($minimum_bid_amount, 2) . " (reason: $minimum_bid_reason)<br>";

    // Check if $84,899.50 matches any of these values
    if (abs($minimum_bid_amount - 84899.50) < 0.01) {
        echo "<strong style='color: red;'>FOUND THE ISSUE: Calculated minimum matches the problematic $84,899.50!</strong><br>";
    } else {
        echo "<strong style='color: orange;'>Calculated minimum does not match $84,899.50 - issue may be elsewhere</strong><br>";
    }
} else {
    echo "3. ERROR: Could not find auction with lot_id: $test_lot_id<br>";
}

echo "</div>";

// Test 2: Test bid validation logic
echo "<h2>Test 2: Bid Validation Logic</h2>";

// Simulate bid data for testing
$test_bid_data = array(
    'lot_id' => $test_lot_id,
    'user_id' => $test_user_id,
    'user_email' => '<EMAIL>',
    'user_name' => 'Test User',
    'bid_amount' => 100.00 // This should be adjusted based on your test scenario
);

echo "<h3>Testing bid validation with rejected bid consideration:</h3>";
$validation_result = USF_Bid_Manager::validate_bid($test_bid_data);

echo "Validation result: " . ($validation_result['valid'] ? 'VALID' : 'INVALID') . "<br>";
if (!$validation_result['valid']) {
    echo "Errors:<br>";
    foreach ($validation_result['errors'] as $error) {
        echo "- " . esc_html($error) . "<br>";
    }
}

// Test 3: Test AJAX validation
echo "<h2>Test 3: AJAX Validation</h2>";

echo "<h3>Testing AJAX bid validation:</h3>";

// Simulate AJAX request data
$_POST['lot_id'] = $test_lot_id;
$_POST['bid_amount'] = 50.00; // Low amount to test rejection

// Capture output
ob_start();
USF_Ajax_Handlers::handle_validate_bid();
$ajax_output = ob_get_clean();

echo "AJAX validation output:<br>";
echo "<pre>" . esc_html($ajax_output) . "</pre>";

// Test 4: Display test instructions
echo "<h2>Manual Testing Instructions</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h3>To fully test the rejected bid validation:</h3>";
echo "<ol>";
echo "<li><strong>Create a test auction</strong> with a minimum offer (e.g., $50)</li>";
echo "<li><strong>Submit a bid</strong> as a test user (e.g., $75)</li>";
echo "<li><strong>Reject the bid</strong> in the admin panel</li>";
echo "<li><strong>Try to submit a lower bid</strong> (e.g., $60) - this should be rejected</li>";
echo "<li><strong>Try to submit a higher bid</strong> (e.g., $80) - this should be accepted</li>";
echo "<li><strong>Test on both shortcodes:</strong>";
echo "<ul>";
echo "<li>[singleauction] - Test the main bid form</li>";
echo "<li>[allauctions] - Test the quick bid functionality</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

// Test 5: Check for required functions
echo "<h2>Test 4: Function Availability Check</h2>";

$required_functions = array(
    'USF_Database::get_user_highest_rejected_bid_amount',
    'USF_Database::get_user_highest_rejected_bid_amount_by_email',
    'USF_Bid_Manager::validate_bid',
    'USF_Ajax_Handlers::handle_validate_bid',
    'USF_Ajax_Handlers::handle_get_user_bid_status'
);

echo "<h3>Checking if all required functions exist:</h3>";
foreach ($required_functions as $function) {
    $exists = method_exists(explode('::', $function)[0], explode('::', $function)[1]);
    echo $function . ": " . ($exists ? "✓ EXISTS" : "✗ MISSING") . "<br>";
}

// Test 5: Display inconsistency fix verification
echo "<h2>Test 5: Display Inconsistency Fix</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #2196F3;'>";
echo "<h3>Fixed Issue: Bid Form Label Consistency</h3>";
echo "<p><strong>Problem:</strong> The [singleauction] bid form showed incorrect minimum amounts in labels for rejected bids.</p>";
echo "<p><strong>Solution:</strong> Updated bid-form.php to:</p>";
echo "<ul>";
echo "<li>Calculate minimum bid amount specifically for rejected bid scenarios</li>";
echo "<li>Display the correct rejected bid amount in form labels and help text</li>";
echo "<li>Ensure input field min/placeholder attributes match the displayed minimum</li>";
echo "</ul>";
echo "<p><strong>To verify the fix:</strong></p>";
echo "<ol>";
echo "<li>Create a test auction and submit a bid</li>";
echo "<li>Have the bid rejected by admin</li>";
echo "<li>View the [singleauction] shortcode bid form</li>";
echo "<li>Verify that:</li>";
echo "<ul>";
echo "<li>The 'Previous Bid' shows the rejected amount</li>";
echo "<li>The input label shows 'Minimum: $X.XX' where X.XX is rejected_bid + 0.01</li>";
echo "<li>The help text says 'Must be higher than your previous rejected bid of $X.XX'</li>";
echo "<li>All amounts match and are consistent</li>";
echo "</ul>";
echo "</ol>";
echo "</div>";

echo "<h2>Test Complete</h2>";
echo "<p><strong>Note:</strong> This test provides basic validation. For complete testing, please follow the manual testing instructions above.</p>";
?>
